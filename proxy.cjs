const puppeteer = require("puppeteer-extra");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
const stealth = StealthPlugin();
stealth.enabledEvasions.delete("navigator.plugins"); // Disable navigator.plugins evasion
stealth.enabledEvasions.add("navigator.permissions"); // Ensure navigator.permissions evasion is enabled
puppeteer.use(stealth);
const { simpleParser } = require("mailparser");
const { promisify } = require("util");
const mysql = require("mysql2/promise");
const { faker } = require("@faker-js/faker");
const UserAgent = require("user-agents");
const { parse } = require("useragent");

const pool = mysql.createPool({
  host: "************",
  user: "otp",
  password: "46EGpepWDwxGeNfK",
  database: "otp",
});

(async () => {
  // Proxy configuration
  const proxyServer = "gw.dataimpulse.com:823";
  const proxyUsername = "888b677829c7ffc4684b";
  const proxyPassword = "3a07caeb6bae627b";


  while (true) {
    const browser = await puppeteer.launch({
      executablePath:
        "C:/Program Files/BraveSoftware/Brave-Browser/Application/brave.exe",
      headless: false,
      args: [
        "--incognito",
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-infobars",
        "--window-position=0,0",
        "--ignore-certificate-errors",
        // "--ignore-certificate-errors-spki-list",
        // "--disable-sync",
        "--disable-features=IsolateOrigins,site-per-process", // Helps with some proxy issues
        "--disable-web-security", // Helps with CORS issues when using proxies
        `--proxy-server=${proxyServer}`,
      ],
    });

    const [page] = await browser.pages();

    // Authenticate with the proxy
    await page.authenticate({
      username: proxyUsername,
      password: proxyPassword,
    });

    page.on("console", (message) => {
      const type = message.type().substr(0, 3).toUpperCase();
      const colors = {
        LOG: "\x1b[37m",
        ERR: "\x1b[31m",
        WAR: "\x1b[33m",
        INF: "\x1b[34m",
      };
      const color = colors[type] || colors.LOG;
      console.log(`${color}${type} ${message.text()}\x1b[0m`);

      // If you want to capture stack traces for errors
      if (message.type() === "error") {
        //  console.log(message.location());
        //  console.log(message.stackTrace());
      }
    });

    await page.setBypassCSP(true);
    await page.setRequestInterception(true);

    // Request interception logic
    page.on("request", (request) => {
      try {
        // Block Criteo sync requests that are slowing down the page
        if (request.url().includes("gum.criteo.com/syncframe")) {
          request.abort();
        }
        // Abort preflight OPTIONS requests to avoid CORS issues
        else if (request.method() === "OPTIONS") {
          //  request.abort();
          request.continue(); // Continue instead of commenting out
        } else {
          // Modify request headers if needed
          const headers = Object.assign({}, request.headers(), {
            Origin: "https://in.bookmyshow.com", // Adjust the Origin header
            Referer: "https://in.bookmyshow.com",
          });
          // Continue the request with modified headers
          request.continue({ headers });
        }
      } catch (error) {
        // Log the error but don't crash the application
        console.error(`Request handling error: ${error.message}`);
        // No need to handle the request again if it's already handled
      }
    });

    page.on("response", (response) => {
      if (response.status() === 401) {
        // Handle authentication failure
        console.log(
          "***************Authentication required*******************"
        );
      }
    });

    await page.setUserAgent(
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    );

    await page.setExtraHTTPHeaders({
      "Accept-Language": "en-US,en;q=0.9",
      "Accept-Encoding": "gzip, deflate, br",
      Referer: "https://in.bookmyshow.com/",
    });

    await page.evaluateOnNewDocument(() => {
      Object.defineProperty(navigator, "hardwareConcurrency", {
        get: () => 4,
      });
      Object.defineProperty(navigator, "deviceMemory", {
        get: () => 8,
      });
    });

    // Check and print the actual IP address being used through proxy
    try {
      console.log("🌐 Checking external IP address through proxy...");
      await page.goto("https://httpbin.org/ip", {
        waitUntil: "networkidle2",
        timeout: 30000,
      });

      const ipContent = await page.content();
      const ipMatch = ipContent.match(/"origin": "([^"]+)"/);
      if (ipMatch) {
        console.log(`✅ External IP through proxy: ${ipMatch[1]}`);
      } else {
        console.log("❌ Could not determine external IP");
      }
    } catch (error) {
      console.log(`⚠️  IP check failed: ${error.message}`);
    }

    const movie_url =
      "https://in.bookmyshow.com/movies/mumbai/oneness-amata-oina/**********"; //await getmovieurl();
    // Step 1: Open the URL
    await page.goto(movie_url, {
      waitUntil: "networkidle2",
    });

    // Step 2: Wait for the page to load
    await page.evaluate(() => {
      const buttons = [...document.querySelectorAll("button")];
      const targetButton = buttons.find((btn) =>
        btn.innerText.includes("I'm interested")
      );
      if (targetButton) {
        targetButton.click();
      }
    });

    try {
      // Step 4: Wait for the popup to appear
      await page.waitForSelector('img[alt="email logo"]');
      console.log("Email logo found, proceeding...");
    } catch (error) {
      console.log("Email logo not found, exiting...");
      process.exit(1);
    }
    await sleep(2000);
    // Step 5: Click on "Continue with Email"
    async function clickEmailLogoAndRestart() {
      try {
        await page.click('img[alt="email logo"]');
        console.log("Email logo clicked, proceeding...");
      } catch (error) {
        console.log("Email click logo not found, exiting...");
        process.exit(1);
      }
    }
    await clickEmailLogoAndRestart();

    // Step 6: Enter random email address
    const randomEmail = await generateRandomEmail();
    // await page.waitForSelector('input[type="email"]');
    await page.keyboard.type(randomEmail); //await page.type('input[type="email"]', randomEmail);
    console.log("email sent on: " + randomEmail);
    // Step 7: Wait 2 seconds and press Tab, then find and click "Continue" button
    await sleep(2000);
    await page.keyboard.press("Enter");
    // await page.waitForSelector('button:contains("Continue")');
    // await page.click('button:contains("Continue")');

    // Step 8: Wait and keep checking to capture OTP from email
    const otp = await checkForOtp(randomEmail);

    // Step 9: Type OTP on page
    //await page.waitForSelector('input[type="text"][name="otp"]');
    await page.keyboard.type(otp, { delay: 100 });
    await sleep(3000);
    // Step 10: Wait for the popup to close (or manually trigger it)
    await page.evaluate(() => {
      const buttons = [...document.querySelectorAll("button")];
      const targetButton = buttons.find((btn) =>
        btn.innerText.includes("I'm interested")
      );
      if (targetButton) {
        targetButton.click();
      }
    });
    await sleep(2000);
    // Cleanup
    await browser.close();
    await sleep(2000);
  }
})();

// Function to pick a random domain and generate a random email ID
async function generateRandomEmail() {
  // Pick a random domain
  const randomDomain = await getdomains();
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  return `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${lastName.toLowerCase()}.${randomDomain}`;
  //   const url = new URL("https://app.addy.io/api/v1/aliases");

  //   const headers = {
  //     Authorization:
  //       "Bearer addy_io_dCU7mWPzImXxn6F1TZpbuYMe6tDn2c9I50OjxHpm34da2671", // Add your Bearer token here
  //     "Content-Type": "application/json",
  //     "X-Requested-With": "XMLHttpRequest",
  //     Accept: "application/json",
  //   };

  //   let body = {
  //     domain: randomDomain,
  //     description: "For example.com",
  //     format: "random_words",
  //   };

  //   try {
  //     const response = await fetch(url, {
  //       method: "POST",
  //       headers,
  //       body: JSON.stringify(body),
  //     });

  //     if (!response.ok) {
  //       throw new Error(`HTTP error! Status: ${response.status}`);
  //     }

  //     const data = await response.json();
  //     console.log("Retrieved data:", data.data.email);

  //     // Return the generated email
  //     return data.data.email;
  //   } catch (error) {
  //     console.error("Error:", error);
  //     return null; // Return null in case of an error
  //   }
}

async function sleep(ms) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}
// Function to check email and capture OTP
async function checkForOtp(email) {
  const pollInterval = 10000; // 10 seconds
  const timeout = 120000; // 2 minutes (120 seconds)
  const startTime = Date.now();
  let otp;

  while (!otp) {
    try {
      const elapsedTime = Date.now() - startTime;
      if (elapsedTime >= timeout) {
        console.log("Timeout exceeded, no OTP found");
        return "000000"; // Return null or a custom message if timeout is reached
      }

      const connection = await pool.getConnection();
      const [rows] = await connection.execute(
        "SELECT otp FROM emails WHERE to_mail = ?",
        [email]
      );
      connection.release();

      if (rows.length > 0) {
        otp = rows[0].otp;
        console.log("OTP retrieved:", otp);
        return otp; // Return the OTP once found
      } else {
        console.log("OTP not found yet, retrying...");
      }

      // Wait for the specified interval before checking again
      await new Promise((resolve) => setTimeout(resolve, pollInterval));
    } catch (error) {
      console.error("Error checking for OTP:", error);
      // You can choose to break the loop on certain errors or continue
    }
  }
}

async function getmovieurl() {
  const connection = await pool.getConnection();
  const [rows] = await connection.execute(
    "SELECT url FROM movie ORDER BY RAND( ) limit 1"
  );
  connection.release();
  return rows[0].url;
}

async function getdomains() {
  const connection = await pool.getConnection();
  const [rows] = await connection.execute(
    "SELECT name FROM domains WHERE status = 1 ORDER BY RAND( ) LIMIT 1"
  );
  connection.release();
  return rows[0].name;
}
