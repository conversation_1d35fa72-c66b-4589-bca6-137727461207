// Load environment variables
require("dotenv").config();

// Browserless configuration
module.exports = {
  // Browserless service URL and token
  BROWSERLESS_URL: process.env.BROWSERLESS_URL || "wss://chrome.browserless.io",
  BROWSERLESS_TOKEN:
    process.env.BROWSERLESS_TOKEN || "YOUR_BROWSERLESS_TOKEN_HERE",

  // Proxy configuration
  PROXY: {
    server: "gw.dataimpulse.com:823",
    username: "888b677829c7ffc4684b",
    password: "3a07caeb6bae627b",
  },

  // Browser options for browserless
  getBrowserlessEndpoint: function (options = {}) {
    const params = new URLSearchParams({
      token: this.BROWSERLESS_TOKEN,
      stealth: true,
      // Add proxy server as a launch argument
      "--proxy-server": this.PROXY.server,
      // Additional browser arguments
      "--no-sandbox": true,
      "--disable-setuid-sandbox": true,
      "--disable-infobars": true,
      "--ignore-certificate-errors": true,
      "--disable-features": "IsolateOrigins,site-per-process",
      "--disable-web-security": true,
      // Custom options
      ...options,
    });

    return `${this.BROWSERLESS_URL}?${params.toString()}`;
  },

  // Alternative method for HTTP API (if you prefer REST over WebSocket)
  getHttpEndpoint: function () {
    return `https://chrome.browserless.io/content?token=${this.BROWSERLESS_TOKEN}`;
  },

  // Default page options
  PAGE_OPTIONS: {
    waitUntil: "networkidle2",
    timeout: 30000,
  },

  // User agent and headers
  USER_AGENT:
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",

  EXTRA_HEADERS: {
    "Accept-Language": "en-US,en;q=0.9",
    "Accept-Encoding": "gzip, deflate, br",
    Referer: "https://in.bookmyshow.com/",
  },
};
