on jp server open database : http://************:888/phpmyadmin_e920916c5f3c50a9
opt table and change movie url

on rise1 server
cd bms
pm2 start email.js --cron-restart="*/30 * * * *"

cd droplet
change .env for number of droplet
pm2 start main.js


project installed on largesender , bulky

open index.js
change bms url 

mail mail id is hosted on all@navi on laravelhosting , delete all old mail

until few mail received it will give error ignore it and restart pm2 all few times

delete all otps from database 

http://*************:888/phpmyadmin_3ced3e2507266f25/index.php?lang=en
324daa4d348aec10
http://*************:888/phpmyadmin_d1236700a667cf68/index.php?lang=en
1ef1e31bce27a5e6

pm2 start index.js -i max --cron-restart="*/30 * * * *"

pm2 start email.js --cron-restart="*/30 * * * *"

pm2 restart all

pm2 start index.cjs -i 2 --cron-restart="*/30 * * * *" --parallel 1 --wait-ready --listen-timeout 15000


sudo find /tmp -type d -name "*puppeteer_dev*" -exec rm -r {} +
run command to delete pupeteer dev profiles

