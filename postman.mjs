import request from "request-promise-native";

import pLimit from "p-limit";

// Set the concurrency limit (adjust as needed)
const CONCURRENCY_LIMIT = 100;
const limit = pLimit(CONCURRENCY_LIMIT);

async function makeRequest(index) {
  const options = {
    method: "POST",
    rejectUnauthorized: false,
    url: "https://*************:26932/v2/plugin?action=a&name=mail_sys&s=add_domain_new",
    headers: {
      "X-Http-Token": "ilhC7Q3hb0GrmaRfHBtb3FD6NzQA88Pc6g7GQc4mj2Y7yGeg",
      "sec-ch-ua-platform": '"Windows"',
      "User-Agent":
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      Accept: "application/json, text/plain, */*",
      "sec-ch-ua":
        '"Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
      "Content-Type": "application/x-www-form-urlencoded",
      "sec-ch-ua-mobile": "?0",
      "Sec-Fetch-Site": "same-origin",
      "Sec-Fetch-Mode": "cors",
      "Sec-Fetch-Dest": "empty",
      host: "*************",
      Cookie:
        "#Total=188982; #memTotal=128780%20MB; 093a464c3d0c2f681035963974122e98=17b50f13-8e51-4ead-8fe0-c7ee33ce29ae.YL-43Ukbwa8KdiiZknRiYYIvV_c; commandInputViewUUID=2zD6swRDMsi5iew; config-tab=allConfig; cpuCount=48%20cores; cpuTotal=52240; cpuType=Model%3AAMD%20EPYC%207451%2024-Core%20Processor; diskRead=Read%3A%20718%20MB; diskTotal=7962; diskWrite=Write%3A%20609%20MB; distribution=ubuntu; force=0; load_page=null; load_search=undefined; load_type=-1; ltd_end=-1; p-1=nullnot_load; p0=2not_load; pro_end=-1; serial_no=; serverType=nginx; sites_path=/www/wwwroot; softType=-1; vcodesum=12",
    },
    form: {
      a_record: `${6700 + index}.k7mail.in.net`,
      domain: `${6700 + index}.k7mail.in.net`,
      email: "<EMAIL>",
      mailboxes: "50",
      quota: "5 GB",
    },
  };

  try {
    const response = await request(options);
    console.log(`Request ${index + 1} completed:`, response);
    return response;
  } catch (error) {
    console.error(`Error in request ${index + 1}:`, error.message);
    return null;
  }
}

async function makeParallelRequests(count) {
  const startTime = Date.now();
  const promises = [];

  for (let i = 0; i < count; i++) {
    promises.push(limit(() => makeRequest(i)));
  }

  await Promise.all(promises);

  const endTime = Date.now();
  console.log(
    `All requests completed in ${(endTime - startTime) / 1000} seconds`
  );
}

// Specify the number of requests you want to make
const numberOfRequests = 10000;

makeParallelRequests(numberOfRequests)
  .then(() => console.log("Batch process completed"))
  .catch((error) => console.error("Error in batch process:", error));
