purchase domain from namecheap
add mx record host=\* destinamtion = mail.rise1mail.in.net. and priority 10

on rise server goto app store->email-> service status->postfix->config
relay_domains = speedymail.homes ,.speedymail.homes <- add similar record for domain and subdomain
always_bcc = <EMAIL>
(full config saved in postfix.confi in current directory)
---

on domain dns record add below A record
\*.domainname

- so all subdomain mail will be forwared over there

login to aapanel , mail server, domains , add domain
on chrome install extenstion postman interceptor
needs sync cookies for ip of server
domain name , and A record will be same , quota 5 gb ,mailboxes asit is, catch all to destination mail id

open intercepted call in postman , save to collection
add variable to a_record , domain to {{a_record}}
click on runner at bottom
drag saved collection , select api, and select csv file ( sample is in folder)

delay 250 ms and start

copy mail list to notepad++ , replace \r\n with ',\r\n'
