# Browserless Setup Guide

This guide explains how to use browserless with your Book My Show automation script.

## What is Browserless?

Browserless is a cloud service that provides headless Chrome instances via WebSocket connections. It eliminates the need to install and manage Chrome locally, provides better performance, and includes built-in stealth features.

## Benefits of Using Browserless

1. **No Local Chrome Installation**: No need to install Chrome/Brave locally
2. **Built-in Stealth**: Automatic stealth features to avoid detection
3. **Better Performance**: Optimized Chrome instances in the cloud
4. **Scalability**: Easy to scale across multiple instances
5. **Reliability**: Professional infrastructure with high uptime

## Setup Instructions

### 1. Get a Browserless Account

1. Go to [browserless.io](https://browserless.io)
2. Sign up for an account
3. Get your API token from the dashboard

### 2. Configure Environment Variables

**Option A: Use the Setup Wizard (Recommended)**

```bash
node setup-browserless.cjs
```

**Option B: Manual Setup**

1. Copy `.env.example` to `.env`:

   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your browserless token:
   ```
   BROWSERLESS_TOKEN=your_actual_token_here
   ```

### 3. Run Your Script

The script `bl.cjs` is now configured to use browserless. Simply run:

```bash
node bl.cjs
```

## Configuration Options

### Browserless Configuration (`browserless-config.cjs`)

- **BROWSERLESS_URL**: WebSocket endpoint for browserless service
- **BROWSERLESS_TOKEN**: Your API token
- **PROXY**: Proxy configuration (server, username, password)
- **USER_AGENT**: Browser user agent string
- **EXTRA_HEADERS**: Additional HTTP headers
- **PAGE_OPTIONS**: Default page navigation options

### Custom Options

You can modify `browserless-config.cjs` to:

- Change proxy settings
- Modify browser arguments
- Update user agent strings
- Adjust timeout settings

## Troubleshooting

### Connection Issues

1. **Invalid Token**: Make sure your browserless token is correct
2. **Network Issues**: Check your internet connection
3. **Proxy Issues**: Verify proxy credentials are correct

### Performance Issues

1. **Timeout Errors**: Increase timeout values in `PAGE_OPTIONS`
2. **Memory Issues**: Browserless handles memory management automatically
3. **Rate Limiting**: Check your browserless plan limits

## Alternative: Self-Hosted Browserless

If you prefer to run browserless locally:

1. Install Docker
2. Run browserless container:
   ```bash
   docker run -p 3000:3000 browserless/chrome
   ```
3. Update `.env`:
   ```
   BROWSERLESS_URL=ws://localhost:3000
   BROWSERLESS_TOKEN=
   ```

## Monitoring

Browserless provides:

- Real-time session monitoring
- Usage analytics
- Error logging
- Performance metrics

Access these through your browserless dashboard.

## Cost Optimization

- Use session limits to control costs
- Monitor usage through the dashboard
- Consider self-hosting for high-volume usage
- Use appropriate timeout settings to avoid hanging sessions
