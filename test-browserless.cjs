// Test script to verify browserless connection
const puppeteer = require("puppeteer-core");
const browserlessConfig = require("./browserless-config.cjs");

async function testBrowserlessConnection() {
  console.log("Testing browserless connection...");

  // Check if token is configured
  if (browserlessConfig.BROWSERLESS_TOKEN === "YOUR_BROWSERLESS_TOKEN_HERE") {
    console.error("❌ Browserless token not configured!");
    console.error(
      "Please set your BROWSERLESS_TOKEN in .env file or environment variables"
    );
    console.error("Get your token from: https://browserless.io");
    process.exit(1);
  }

  try {
    // Connect to browserless service
    console.log("Connecting to:", browserlessConfig.getBrowserlessEndpoint());

    const browser = await puppeteer.connect({
      browserWSEndpoint: browserlessConfig.getBrowserlessEndpoint(),
      defaultViewport: null,
    });

    console.log("✅ Successfully connected to browserless!");

    // Create a new page
    const page = await browser.newPage();

    // Set user agent
    await page.setUserAgent(browserlessConfig.USER_AGENT);

    // Navigate to a test page
    console.log("Navigating to test page...");
    await page.goto("https://httpbin.org/user-agent", {
      waitUntil: "networkidle2",
    });

    // Get the page content
    const content = await page.content();
    console.log("✅ Page loaded successfully!");

    // Extract user agent from the response
    const userAgentMatch = content.match(/"user-agent": "([^"]+)"/);
    if (userAgentMatch) {
      console.log("User Agent:", userAgentMatch[1]);
    }

    // Test proxy if configured
    if (browserlessConfig.PROXY.server) {
      console.log("Testing proxy connection...");
      try {
        await page.authenticate({
          username: browserlessConfig.PROXY.username,
          password: browserlessConfig.PROXY.password,
        });

        // Navigate to IP check service
        await page.goto("https://httpbin.org/ip", {
          waitUntil: "networkidle2",
        });
        const ipContent = await page.content();
        const ipMatch = ipContent.match(/"origin": "([^"]+)"/);
        if (ipMatch) {
          console.log("✅ Proxy working! IP:", ipMatch[1]);
        }
      } catch (proxyError) {
        console.log("⚠️  Proxy test failed:", proxyError.message);
      }
    }

    // Close browser
    await browser.close();
    console.log("✅ Test completed successfully!");
  } catch (error) {
    console.error("❌ Browserless connection failed:");
    console.error("Error:", error.message);

    if (error.message.includes("429")) {
      console.error("\n💡 Rate limit exceeded. This could mean:");
      console.error("   - Invalid or expired token");
      console.error("   - Free tier limits reached");
      console.error("   - Too many concurrent sessions");
      console.error("   - Check your browserless dashboard for usage");
    } else if (error.message.includes("401") || error.message.includes("403")) {
      console.error("\n💡 Authentication failed:");
      console.error("   - Check your BROWSERLESS_TOKEN is correct");
      console.error("   - Make sure token is not expired");
    } else if (error.message.includes("token")) {
      console.error(
        "\n💡 Tip: Make sure your BROWSERLESS_TOKEN is set correctly in .env file"
      );
    } else if (error.message.includes("ECONNREFUSED")) {
      console.error(
        "\n💡 Tip: Check your internet connection and browserless service status"
      );
    }

    console.error("\n📖 For setup help, see: BROWSERLESS_SETUP.md");
    process.exit(1);
  }
}

// Run the test
testBrowserlessConnection();
