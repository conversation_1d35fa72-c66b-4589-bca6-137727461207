# See /usr/share/postfix/main.cf.dist for a commented, more complete version


# Debian specific:  Specifying a file name will cause the first
# line of that file to be used as the name.  The Debian default
# is /etc/mailname.
#myorigin = /etc/mailname

smtpd_banner = $myhostname ESMTP $mail_name (Ubuntu)
biff = no

# appending .domain is the MUA's job.
append_dot_mydomain = no

# Uncomment the next line to generate "delayed mail" warnings
#delay_warning_time = 4h

readme_directory = no

# See http://www.postfix.org/COMPATIBILITY_README.html -- default to 3.6 on
# fresh installs.
compatibility_level = 3.6



# TLS parameters
smtpd_tls_cert_file=/etc/ssl/certs/ssl-cert-snakeoil.pem
smtpd_tls_key_file=/etc/ssl/private/ssl-cert-snakeoil.key
smtpd_tls_security_level = may

smtp_tls_CApath=/etc/ssl/certs
smtp_tls_security_level = may
smtp_tls_session_cache_database = btree:${data_directory}/smtp_scache


smtpd_relay_restrictions = permit_mynetworks permit_sasl_authenticated defer_unauth_destination
myhostname = waba.bulkymarketing.com
alias_maps = hash:/etc/aliases
alias_database = hash:/etc/aliases
mydestination = 
relayhost = 
mynetworks = *********/8 [::ffff:*********]/104 [::1]/128
mailbox_size_limit = 0
recipient_delimiter = +
inet_interfaces = all
inet_protocols = all
virtual_mailbox_domains = sqlite:/etc/postfix/sqlite_virtual_domains_maps.cf
virtual_alias_maps= sqlite:/etc/postfix/btrule.cf
virtual_mailbox_maps = sqlite:/etc/postfix/sqlite_virtual_mailbox_maps.cf, sqlite:/etc/postfix/sqlite_virtual_alias_domain_mailbox_maps.cf
smtpd_sasl_type = dovecot
smtpd_sasl_path = private/auth
smtpd_sasl_auth_enable = no
smtpd_recipient_restrictions = permit_sasl_authenticated, permit_mynetworks, reject_unauth_destination
relay_domains = rise1mail.in.net, .rise1mail.in.net , gmail.com , outlook.com ,speedymail.homes ,.speedymail.homes ,fastmail.monster ,.fastmail.monster ,g-mail.click,.g-mail.click,inboxexpress.beauty,.inboxexpress.beauty,inboxguardian.boats,.inboxguardian.boats,inboxmasterpro.boats,.inboxmasterpro.boats,inboxmasterpro.online,.inboxmasterpro.online,justmail.fun,.justmail.fun,mailenterprise.shop,.mailenterprise.shop,mailman.lol,.mailman.lol,mailmasterpro.store,.mailmasterpro.store,mailwizpro.boats,.mailwizpro.boats,messagemaster.beauty,.messagemaster.beauty,messageprime.autos,.messageprime.autos,quickmail.cyou,.quickmail.cyou,quickmail.sbs,.quickmail.sbs,rapidmailpro.beauty,.rapidmailpro.beauty,rapidmailpro.boats,.rapidmailpro.boats,speedymail.homes,.speedymail.homes,swiftcommpro.mom,.swiftcommpro.mom,swiftinboxpro.autos,.swiftinboxpro.autos,swiftsend.autos,.swiftsend.autos,wohomail.cfd,.wohomail.cfd,wondermail.site,.wondermail.site,workmail.yachts,.workmail.yachts,zmail.hair,.zmail.hair,omanapita.cam,.omanapita.cam,gutaria.wiki,.gutaria.wiki,tyurita.rest,.tyurita.rest,yugandalora.life,.yugandalora.life,quotarest.world,.quotarest.world,jakustiman.icu,.jakustiman.icu,zumalocorp.website,.zumalocorp.website,uamanarota.bond,.uamanarota.bond,yukonova.xyz,.yukonova.xyz,yutura.pics,.yutura.pics,raotsa.quest,.raotsa.quest
smtpd_use_tls = yes
virtual_transport = lmtp:unix:private/dovecot-lmtp
smtpd_milters = inet:127.0.0.1:11332
non_smtpd_milters = inet:127.0.0.1:11332
milter_mail_macros = i {mail_addr} {client_addr} {client_name} {auth_authen}
milter_protocol = 6
milter_default_action = accept
message_size_limit = 102400000
smtpd_client_connection_count_limit = 200
smtpd_client_connection_rate_limit = 1000
always_bcc = <EMAIL>

recipient_bcc_maps = hash:/etc/postfix/recipient_bcc
sender_bcc_maps = hash:/etc/postfix/sender_bcc
