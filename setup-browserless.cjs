// Setup script for browserless configuration
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setupBrowserless() {
  console.log('🚀 Browserless Setup Wizard');
  console.log('============================\n');
  
  console.log('This wizard will help you configure browserless for your project.\n');
  
  // Check if .env already exists
  const envPath = path.join(__dirname, '.env');
  const envExists = fs.existsSync(envPath);
  
  if (envExists) {
    console.log('⚠️  .env file already exists.');
    const overwrite = await question('Do you want to overwrite it? (y/N): ');
    if (overwrite.toLowerCase() !== 'y' && overwrite.toLowerCase() !== 'yes') {
      console.log('Setup cancelled.');
      rl.close();
      return;
    }
  }
  
  console.log('\n📝 Please provide your browserless configuration:\n');
  
  // Get browserless token
  console.log('1. Get your browserless token:');
  console.log('   - Go to https://browserless.io');
  console.log('   - Sign up for an account');
  console.log('   - Copy your API token from the dashboard\n');
  
  const token = await question('Enter your browserless token: ');
  
  if (!token || token.trim() === '') {
    console.log('❌ Token is required. Setup cancelled.');
    rl.close();
    return;
  }
  
  // Optional: Custom browserless URL
  console.log('\n2. Browserless URL (optional):');
  const customUrl = await question('Enter custom browserless URL (press Enter for default): ');
  
  // Create .env content
  let envContent = `# Browserless Configuration
BROWSERLESS_TOKEN=${token.trim()}
`;
  
  if (customUrl && customUrl.trim() !== '') {
    envContent += `BROWSERLESS_URL=${customUrl.trim()}\n`;
  }
  
  envContent += `
# Database configuration (optional overrides)
# DB_HOST=************
# DB_USER=otp
# DB_PASSWORD=46EGpepWDwxGeNfK
# DB_NAME=otp
`;
  
  // Write .env file
  try {
    fs.writeFileSync(envPath, envContent);
    console.log('\n✅ .env file created successfully!');
    
    // Test the connection
    console.log('\n🧪 Testing browserless connection...');
    
    // Import and test
    const { spawn } = require('child_process');
    const testProcess = spawn('node', ['test-browserless.cjs'], {
      stdio: 'inherit'
    });
    
    testProcess.on('close', (code) => {
      if (code === 0) {
        console.log('\n🎉 Setup completed successfully!');
        console.log('\nYou can now run your automation script:');
        console.log('   node bl.cjs');
      } else {
        console.log('\n⚠️  Setup completed but connection test failed.');
        console.log('Please check your token and try running: node test-browserless.cjs');
      }
      rl.close();
    });
    
  } catch (error) {
    console.error('❌ Failed to create .env file:', error.message);
    rl.close();
  }
}

// Run setup
setupBrowserless().catch(console.error);
