# Browserless Configuration
# Copy this file to .env and fill in your actual values

# Browserless service URL (WebSocket endpoint)
BROWSERLESS_URL=wss://chrome.browserless.io

# Your browserless token (get this from your browserless account)
BROWSERLESS_TOKEN=YOUR_BROWSERLESS_TOKEN_HERE

# Alternative: If using a self-hosted browserless instance
# BROWSERLESS_URL=ws://localhost:3000

# Database configuration (if you want to override the hardcoded values)
# DB_HOST=************
# DB_USER=otp
# DB_PASSWORD=46EGpepWDwxGeNfK
# DB_NAME=otp
