import Imap from "node-imap";
import mysql from "mysql2/promise";

const pool = mysql.createPool({
  host: "************",
  user: "otp",
  password: "46EGpepWDwxGeNfK",
  database: "otp",
});

var imap = new Imap({
  user: "<EMAIL>",
  password: "Navin@1234",
  host: "mail.navin-bagdi.in.net",
  port: 143,
  tls: false,
});

async function storeEmail(id, to, from, date, otp) {
  try {
    const connection = await pool.getConnection();
    await connection.execute(
      "INSERT IGNORE INTO emails (to_mail, from_mail, date, otp) VALUES (?, ?, ?, ?)",
      [to, from, date, otp]
    );
    connection.release();
  } catch (error) {
    console.error("Error storing email:", error);
  }
}

function openInbox(cb) {
  imap.openBox("INBOX", false, cb);
}

async function checkForEmails() {
  openInbox(function (err, box) {
    if (err) throw err;

    // Calculate the start message number, ensuring it doesn't go below 1
    const totalMessages = box.messages.total;
    const startMessage = Math.max(1, totalMessages - 19); // Get last 20 messages or all if less

    // Only proceed if there are messages
    if (totalMessages === 0) {
      console.log("No messages in mailbox");
      return;
    }

    const fetchRange = `${startMessage}:*`;
    var f = imap.seq.fetch(fetchRange, {
      bodies: "HEADER.FIELDS (FROM TO SUBJECT DATE)",
      struct: true,
    });

    f.on("message", function (msg, seqno) {
      console.log("Message #%d", seqno);
      var prefix = "(#" + seqno + ") ";

      msg.on("body", function (stream, info) {
        var buffer = "";
        stream.on("data", function (chunk) {
          buffer += chunk.toString("utf8");
        });

        stream.once("end", function () {
          const parsedHeader = Imap.parseHeader(buffer);
          const fromMatch = String(parsedHeader.from).match(/<([^>]+)>/) ?? [
            "",
            "nomail",
          ];
          const from = fromMatch[1];

          if (from.includes("bookmyshow")) {
            const to = parsedHeader.to[0];
            const otpMatch = String(parsedHeader.subject).match(/\d{6}/) ?? [
              "0",
            ];
            const otp = otpMatch[0];
            const date = String(parsedHeader.date);

            console.log({
              seqno,
              to,
              from,
              date,
              otp,
            });

            // Store email first
            storeEmail(seqno, to, from, date, otp)
              .then(() => {
                // After successful storage, delete the email
                imap.seq.addFlags(seqno, "\\Deleted", (err) => {
                  if (err) {
                    console.error(`Error deleting message #${seqno}:`, err);
                    return;
                  }
                  console.log(`Message #${seqno} marked for deletion`);

                  // Expunge the mailbox to permanently remove all messages marked for deletion
                  imap.expunge((err) => {
                    if (err) {
                      console.error("Error expunging mailbox:", err);
                      return;
                    }
                    console.log("Mailbox expunged successfully");
                  });
                });
              })
              .catch((err) => {
                console.error("Error storing email:", err);
              });
          }
        });
      });

      msg.once("attributes", function (attrs) {
        // Handle attributes if needed
      });

      msg.once("end", function () {
        console.log(prefix + "Finished processing");
      });
    });

    f.once("error", function (err) {
      console.log("Fetch error: " + err);
    });

    f.once("end", function () {
      console.log("Done fetching all messages!");
    });
  });
}

imap.once("ready", function () {
  checkForEmails(); // Initial check
  setInterval(checkForEmails, 10000); // Check every 10 seconds
});

imap.connect();
