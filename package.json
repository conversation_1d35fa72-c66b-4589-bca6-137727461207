{"name": "bms", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@faker-js/faker": "^9.0.3", "@google-cloud/local-auth": "^2.1.0", "bms": "file:", "dotenv": "^16.5.0", "fetch": "^1.1.0", "googleapis": "^105.0.0", "mailparser": "^3.7.1", "mysql2": "^3.11.0", "mysql2-promise": "^0.1.4", "newman": "^6.2.1", "node-imap": "^0.9.6", "p-limit": "^6.1.0", "puppeteer": "^23.0.2", "puppeteer-core": "^24.9.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "request": "^2.88.2", "request-promise-native": "^1.0.9", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "tough-cookie": "^5.0.0", "user-agents": "^1.1.423", "useragent": "^2.3.0"}}