const puppeteer = require("puppeteer-extra");

const StealthPlugin = require("puppeteer-extra-plugin-stealth");
const stealth = StealthPlugin();
stealth.enabledEvasions.delete("navigator.plugins"); // Disable navigator.plugins evasion
stealth.enabledEvasions.add("navigator.permissions"); // Ensure navigator.permissions evasion is enabled
puppeteer.use(stealth);
const { simpleParser } = require("mailparser");
const { promisify } = require("util");
const mysql = require("mysql2/promise");
const { faker } = require("@faker-js/faker");
const UserAgent = require("user-agents");
const { parse } = require("useragent");

const pool = mysql.createPool({
  host: "************",
  user: "otp",
  password: "46EGpepWDwxGeNfK",
  database: "otp",
});

(async () => {
  // const proxyServer = '*************:8080';
  while (true) {
    //         const tmpDir = tmp.dirSync({ prefix: 'chrome_dev_test_' }).name;

    const browser = await puppeteer.launch({
      executablePath: "/usr/bin/brave-browser",
      headless: true,
      args: [
        "--incognito",
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-infobars",
        "--window-position=0,0",
        "--ignore-certificate-errors",
        "--ignore-certifcate-errors-spki-list",
        // "--disable-features=IsolateOrigins,site-per-process",
        // "--disable-web-security", // Add this to disable CORS
        // '--user-data-dir=${tmpDir}'  // Prevent errors with user data dir
        // `--proxy-server=${proxyServer}`
      ],
    });
    const [page] = await browser.pages();
    page.on("console", (message) => {
      const type = message.type().substr(0, 3).toUpperCase();
      const colors = {
        LOG: "\x1b[37m",
        ERR: "\x1b[31m",
        WAR: "\x1b[33m",
        INF: "\x1b[34m",
      };
      const color = colors[type] || colors.LOG;
      console.log(`${color}${type} ${message.text()}\x1b[0m`);

      // If you want to capture stack traces for errors
      if (message.type() === "error") {
        //  console.log(message.location());
        //  console.log(message.stackTrace());
      }
    });

    // Capture uncaught exceptions
    //   page.on('pageerror', error => {
    //     console.error('Page error:', error.message);
    //   });

    //   // Capture network request failures
    //   page.on('requestfailed', request => {
    //     console.error(`Request failed: ${request.url()}`);
    //   });
    await page.setBypassCSP(true);
    await page.setRequestInterception(true);

    // Request interception logic
    page.on("request", (request) => {
      // Abort preflight OPTIONS requests to avoid CORS issues
      if (request.method() === "OPTIONS") {
        //  request.abort();
      } else {
        // Modify request headers if needed
        const headers = Object.assign({}, request.headers(), {
          Origin: "https://in.bookmyshow.com", // Adjust the Origin header
          Referer: "https://in.bookmyshow.com",
        });
        // Continue the request with modified headers
        request.continue({ headers });
      }
    });

    page.on("response", (response) => {
      if (response.status() === 401) {
        // Handle authentication failure
        console.log(
          "***************Authentication required*******************"
        );
      }
    });

    // await page.setUserAgent(
    //   "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    // );

    await page.setExtraHTTPHeaders({
      "Accept-Language": "en-US,en;q=0.9",
      "Accept-Encoding": "gzip, deflate, br",
      Referer: "https://in.bookmyshow.com/",
    });

    await page.evaluateOnNewDocument(() => {
      // check if navigator.webdriver is null or false
      if (navigator.webdriver === null || navigator.webdriver === false) {
        // already patched or not detected, no action needed
      } else {
        // patch navigator.webdriver to false
        Object.defineProperty(navigator, "webdriver", {
          get: () => false,
          configurable: true,
        });
      }
      // customize the navigator.userAgent value
      Object.defineProperty(navigator, "userAgent", {
        value:
          "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        configurable: true,
      });

      // customize the navigator.platform value
      Object.defineProperty(navigator, "platform", {
        value: "Linux x86_64",
        configurable: true,
      });
    });
    const movie_url = await getmovieurl();
    // Step 1: Open the URL
    await page.goto(movie_url, {
      waitUntil: "networkidle2",
    });

    // Step 2: Wait for the page to load
    await page.evaluate(() => {
      const buttons = [...document.querySelectorAll("button")];
      const targetButton = buttons.find((btn) =>
        btn.innerText.includes("I'm interested")
      );
      if (targetButton) {
        
        targetButton.click();
      }
    });

    try {
      // Step 4: Wait for the popup to appear
      await page.waitForSelector('img[alt="email logo"]');
    } catch (error) {
      process.exit(1);
    }

    // Step 5: Click on "Continue with Email"
    async function clickEmailLogoAndRestart() {
      try {
        await page.click('img[alt="email logo"]');
      } catch (error) {
        process.exit(1);
      }
    }
    await clickEmailLogoAndRestart();

    // Step 6: Enter random email address
    const randomEmail = await generateRandomEmail();
    // await page.waitForSelector('input[type="email"]');
    await page.keyboard.type(randomEmail); //await page.type('input[type="email"]', randomEmail);
    console.log("email sent on: " + randomEmail);
    // Step 7: Wait 2 seconds and press Tab, then find and click "Continue" button
    await sleep(2000);
    await page.keyboard.press("Enter");
    // await page.waitForSelector('button:contains("Continue")');
    // await page.click('button:contains("Continue")');

    // Step 8: Wait and keep checking to capture OTP from email
    const otp = await checkForOtp(randomEmail);

    // Step 9: Type OTP on page
    //await page.waitForSelector('input[type="text"][name="otp"]');
    await page.keyboard.type(otp, { delay: 100 });
    await sleep(3000);
    // Step 10: Wait for the popup to close (or manually trigger it)
    await page.evaluate(() => {
      const buttons = [...document.querySelectorAll("button")];
      const targetButton = buttons.find((btn) =>
        btn.innerText.includes("I'm interested")
      );
      if (targetButton) {
        targetButton.click();
      }
    });
    await sleep(2000);
    // Cleanup
    await browser.close();
    await sleep(2000);
  }
})();

// Function to pick a random domain and generate a random email ID
async function generateRandomEmail() {
  // Pick a random domain
  const randomDomain = await getdomains();
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  return `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${lastName.toLowerCase()}.${randomDomain}`;
  //   const url = new URL("https://app.addy.io/api/v1/aliases");

  //   const headers = {
  //     Authorization:
  //       "Bearer addy_io_dCU7mWPzImXxn6F1TZpbuYMe6tDn2c9I50OjxHpm34da2671", // Add your Bearer token here
  //     "Content-Type": "application/json",
  //     "X-Requested-With": "XMLHttpRequest",
  //     Accept: "application/json",
  //   };

  //   let body = {
  //     domain: randomDomain,
  //     description: "For example.com",
  //     format: "random_words",
  //   };

  //   try {
  //     const response = await fetch(url, {
  //       method: "POST",
  //       headers,
  //       body: JSON.stringify(body),
  //     });

  //     if (!response.ok) {
  //       throw new Error(`HTTP error! Status: ${response.status}`);
  //     }

  //     const data = await response.json();
  //     console.log("Retrieved data:", data.data.email);

  //     // Return the generated email
  //     return data.data.email;
  //   } catch (error) {
  //     console.error("Error:", error);
  //     return null; // Return null in case of an error
  //   }
}

async function sleep(ms) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}
// Function to check email and capture OTP
async function checkForOtp(email) {
  const pollInterval = 10000; // 10 seconds
  const timeout = 120000; // 2 minutes (120 seconds)
  const startTime = Date.now();
  let otp;

  while (!otp) {
    try {
      const elapsedTime = Date.now() - startTime;
      if (elapsedTime >= timeout) {
        console.log("Timeout exceeded, no OTP found");
        return "000000"; // Return null or a custom message if timeout is reached
      }

      const connection = await pool.getConnection();
      const [rows] = await connection.execute(
        "SELECT otp FROM emails WHERE to_mail = ?",
        [email]
      );
      connection.release();

      if (rows.length > 0) {
        otp = rows[0].otp;
        console.log("OTP retrieved:", otp);
        return otp; // Return the OTP once found
      } else {
        console.log("OTP not found yet, retrying...");
      }

      // Wait for the specified interval before checking again
      await new Promise((resolve) => setTimeout(resolve, pollInterval));
    } catch (error) {
      console.error("Error checking for OTP:", error);
      // You can choose to break the loop on certain errors or continue
    }
  }
}

async function getmovieurl() {
  const connection = await pool.getConnection();
  const [rows] = await connection.execute(
    "SELECT url FROM movie ORDER BY RAND( ) limit 1"
  );
  connection.release();
  return rows[0].url;
}

async function getdomains() {
  const connection = await pool.getConnection();
  const [rows] = await connection.execute(
    "SELECT name FROM domains WHERE status = 1 ORDER BY RAND( ) LIMIT 1"
  );
  connection.release();
  return rows[0].name;
}
