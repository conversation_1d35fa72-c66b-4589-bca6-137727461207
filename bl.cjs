// Use puppeteer-core for browserless connection
const puppeteer = require("puppeteer-core");
const mysql = require("mysql2/promise");
const { faker } = require("@faker-js/faker");
const browserlessConfig = require("./browserless-config.cjs");

const pool = mysql.createPool({
  host: "************",
  user: "otp",
  password: "46EGpepWDwxGeNfK",
  database: "otp",
});

(async () => {
  while (true) {
    try {
      console.log("Connecting to browserless service...");

      // Connect to browserless service using configuration
      const browser = await puppeteer.connect({
        browserWSEndpoint: browserlessConfig.getBrowserlessEndpoint(),
        defaultViewport: null,
      });

      console.log("Connected to browserless successfully!");
      const [page] = await browser.pages();

      // Authenticate with the proxy
      await page.authenticate({
        username: browserlessConfig.PROXY.username,
        password: browserlessConfig.PROXY.password,
      });

      page.on("console", (message) => {
        const type = message.type().substr(0, 3).toUpperCase();
        const colors = {
          LOG: "\x1b[37m",
          ERR: "\x1b[31m",
          WAR: "\x1b[33m",
          INF: "\x1b[34m",
        };
        const color = colors[type] || colors.LOG;
        console.log(`${color}${type} ${message.text()}\x1b[0m`);

        // If you want to capture stack traces for errors
        if (message.type() === "error") {
          //  console.log(message.location());
          //  console.log(message.stackTrace());
        }
      });

      await page.setBypassCSP(true);
      await page.setRequestInterception(true);

      // Request interception logic
      page.on("request", (request) => {
        try {
          // Block Criteo sync requests that are slowing down the page
          if (request.url().includes("gum.criteo.com/syncframe")) {
            request.abort();
          }
          // Abort preflight OPTIONS requests to avoid CORS issues
          else if (request.method() === "OPTIONS") {
            //  request.abort();
            request.continue(); // Continue instead of commenting out
          } else {
            // Modify request headers if needed
            const headers = Object.assign({}, request.headers(), {
              Origin: "https://in.bookmyshow.com", // Adjust the Origin header
              Referer: "https://in.bookmyshow.com",
            });
            // Continue the request with modified headers
            request.continue({ headers });
          }
        } catch (error) {
          // Log the error but don't crash the application
          console.error(`Request handling error: ${error.message}`);
          // No need to handle the request again if it's already handled
        }
      });

      page.on("response", (response) => {
        if (response.status() === 401) {
          // Handle authentication failure
          console.log(
            "***************Authentication required*******************"
          );
        }
      });

      await page.setUserAgent(browserlessConfig.USER_AGENT);
      await page.setExtraHTTPHeaders(browserlessConfig.EXTRA_HEADERS);

      await page.evaluateOnNewDocument(() => {
        Object.defineProperty(navigator, "hardwareConcurrency", {
          get: () => 4,
        });
        Object.defineProperty(navigator, "deviceMemory", {
          get: () => 8,
        });
      });

      const movie_url =
        "https://in.bookmyshow.com/movies/mumbai/oneness-amata-oina/**********"; //await getmovieurl();
      // Step 1: Open the URL
      await page.goto(movie_url, browserlessConfig.PAGE_OPTIONS);

      // Step 2: Wait for the page to load
      await page.evaluate(() => {
        const buttons = [...document.querySelectorAll("button")];
        const targetButton = buttons.find((btn) =>
          btn.innerText.includes("I'm interested")
        );
        if (targetButton) {
          targetButton.click();
        }
      });

      try {
        // Step 4: Wait for the popup to appear
        await page.waitForSelector('img[alt="email logo"]');
        console.log("Email logo found, proceeding...");
      } catch (error) {
        console.log("Email logo not found, exiting...");
        process.exit(1);
      }
      await sleep(2000);
      // Step 5: Click on "Continue with Email"
      async function clickEmailLogoAndRestart() {
        try {
          await page.click('img[alt="email logo"]');
          console.log("Email logo clicked, proceeding...");
        } catch (error) {
          console.log("Email click logo not found, exiting...");
          process.exit(1);
        }
      }
      await clickEmailLogoAndRestart();

      // Step 6: Enter random email address
      const randomEmail = await generateRandomEmail();
      // await page.waitForSelector('input[type="email"]');
      await page.keyboard.type(randomEmail); //await page.type('input[type="email"]', randomEmail);
      console.log("email sent on: " + randomEmail);
      // Step 7: Wait 2 seconds and press Tab, then find and click "Continue" button
      await sleep(2000);
      await page.keyboard.press("Enter");
      // await page.waitForSelector('button:contains("Continue")');
      // await page.click('button:contains("Continue")');

      // Step 8: Wait and keep checking to capture OTP from email
      const otp = await checkForOtp(randomEmail);

      // Step 9: Type OTP on page
      //await page.waitForSelector('input[type="text"][name="otp"]');
      await page.keyboard.type(otp, { delay: 100 });
      await sleep(3000);
      // Step 10: Wait for the popup to close (or manually trigger it)
      await page.evaluate(() => {
        const buttons = [...document.querySelectorAll("button")];
        const targetButton = buttons.find((btn) =>
          btn.innerText.includes("I'm interested")
        );
        if (targetButton) {
          targetButton.click();
        }
      });
      await sleep(2000);
      // Cleanup
      await browser.close();
      await sleep(2000);
    } catch (error) {
      console.error("Error in automation loop:", error);
      // Wait before retrying
      await sleep(5000);
    }
  }
})();

// Function to pick a random domain and generate a random email ID
async function generateRandomEmail() {
  // Pick a random domain
  const randomDomain = await getdomains();
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  return `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${lastName.toLowerCase()}.${randomDomain}`;
}

async function sleep(ms) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}
// Function to check email and capture OTP
async function checkForOtp(email) {
  const pollInterval = 10000; // 10 seconds
  const timeout = 120000; // 2 minutes (120 seconds)
  const startTime = Date.now();
  let otp;

  while (!otp) {
    try {
      const elapsedTime = Date.now() - startTime;
      if (elapsedTime >= timeout) {
        console.log("Timeout exceeded, no OTP found");
        return "000000"; // Return null or a custom message if timeout is reached
      }

      const connection = await pool.getConnection();
      const [rows] = await connection.execute(
        "SELECT otp FROM emails WHERE to_mail = ?",
        [email]
      );
      connection.release();

      if (rows.length > 0) {
        otp = rows[0].otp;
        console.log("OTP retrieved:", otp);
        return otp; // Return the OTP once found
      } else {
        console.log("OTP not found yet, retrying...");
      }

      // Wait for the specified interval before checking again
      await new Promise((resolve) => setTimeout(resolve, pollInterval));
    } catch (error) {
      console.error("Error checking for OTP:", error);
      // You can choose to break the loop on certain errors or continue
    }
  }
}

async function getmovieurl() {
  const connection = await pool.getConnection();
  const [rows] = await connection.execute(
    "SELECT url FROM movie ORDER BY RAND( ) limit 1"
  );
  connection.release();
  return rows[0].url;
}

async function getdomains() {
  const connection = await pool.getConnection();
  const [rows] = await connection.execute(
    "SELECT name FROM domains WHERE status = 1 ORDER BY RAND( ) LIMIT 1"
  );
  connection.release();
  return rows[0].name;
}
